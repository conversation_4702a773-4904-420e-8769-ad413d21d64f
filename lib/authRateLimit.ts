import { NextRequest } from 'next/server';

interface RateLimitEntry {
  count: number;
  resetTime: number;
}

const authAttempts = new Map<string, RateLimitEntry>();

// Clean up old entries every hour
setInterval(() => {
  const now = Date.now();
  for (const [key, entry] of authAttempts.entries()) {
    if (now > entry.resetTime) {
      authAttempts.delete(key);
    }
  }
}, 60 * 60 * 1000);

export function checkAuthRateLimit(request: NextRequest): { allowed: boolean; retryAfter?: number } {
  const identifier = getClientIdentifier(request);
  const now = Date.now();
  const windowMs = 15 * 60 * 1000; // 15 minutes
  const maxAttempts = 5; // 5 attempts per 15 minutes
  
  const entry = authAttempts.get(identifier);
  
  if (!entry || now > entry.resetTime) {
    authAttempts.set(identifier, {
      count: 1,
      resetTime: now + windowMs
    });
    return { allowed: true };
  }
  
  if (entry.count >= maxAttempts) {
    const retryAfter = Math.ceil((entry.resetTime - now) / 1000);
    return { allowed: false, retryAfter };
  }
  
  entry.count++;
  return { allowed: true };
}

function getClientIdentifier(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for');
  const ip = forwarded ? forwarded.split(',')[0] : request.ip;
  return ip || 'unknown';
}

export function recordFailedAuth(request: NextRequest): void {
  const identifier = getClientIdentifier(request);
  const entry = authAttempts.get(identifier);
  
  if (entry) {
    entry.count++;
  } else {
    const now = Date.now();
    authAttempts.set(identifier, {
      count: 1,
      resetTime: now + 15 * 60 * 1000
    });
  }
}
