import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import Link from "next/link";

// Sanitize error messages to prevent XSS
function sanitizeErrorMessage(error: string): string {
  const allowedErrors = [
    'Authentication failed',
    'Invalid credentials',
    'Account not verified',
    'Session expired',
    'Access denied'
  ];

  // Return generic message if error is not in allowed list
  return allowedErrors.includes(error) ? error : 'Authentication failed. Please try again.';
}

export default async function Page({
  searchParams,
}: {
  searchParams: Promise<{ error: string }>;
}) {
  const params = await searchParams;
  const safeErrorMessage = params?.error ? sanitizeErrorMessage(params.error) : null;

  return (
    <div className="flex min-h-svh w-full items-center justify-center p-6 md:p-10">
      <div className="w-full max-w-sm">
        <div className="flex flex-col gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-2xl">
                Sorry, something went wrong.
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {safeErrorMessage ? (
                  <p className="text-sm text-white">
                    {safeErrorMessage}
                  </p>
                ) : (
                  <p className="text-sm text-white">
                    An unspecified error occurred.
                  </p>
                )}
                <Button asChild className="w-full">
                  <Link href="/auth/login">Back to Login</Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
