import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import OpenAI from 'openai';
import { enforceRateLimit, decrementLimits, logGenerationAttempt } from '@/lib/rateLimiter';

// Hardcoded prompt for text processing
const HARDCODED_PROMPT = "Transform this text into a more emotional and expressive version with deeper feeling and poetic language. Keep the core message but make it more touching and heartfelt:";

export async function POST(request: NextRequest) {
  let userId: number = 0;
  let rateLimitStatus: { 
    userRemaining: number;
    globalRemaining: number;
    userLimit: number;
    globalLimit: number;
  } | null = null;

  try {
    // Check required environment variables
    const openrouterApiKey = process.env.OPENROUTER_API_KEY;
    const model = process.env.MODEL || 'gpt-3.5-turbo';

    if (!openrouterApiKey) {
      return NextResponse.json({ 
        error: 'OpenRouter API key not configured' 
      }, { status: 500 });
    }

    // Initialize OpenAI client for OpenRouter
    const openai = new OpenAI({
      apiKey: openrouterApiKey,
      baseURL: 'https://openrouter.ai/api/v1',
      defaultHeaders: {
        'HTTP-Referer': process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000',
        'X-Title': 'Tears of the Left - Text Processing'
      }
    });

    // Check authentication
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    console.log('🔐 Authenticated user:', user.id);

    // Check rate limits before processing
    console.log('🛡️ Checking rate limits...');
    const rateLimitResult = await enforceRateLimit(user.id);
    userId = rateLimitResult.userId;
    rateLimitStatus = rateLimitResult.status;

    if (!rateLimitResult.allowed) {
      console.log('❌ Rate limit exceeded:', rateLimitResult.status.reason);

      // Log the blocked attempt
      await logGenerationAttempt({
        userId,
        prompt: 'Rate limit exceeded',
        success: false,
        errorMessage: rateLimitResult.status.reason
      });

      return NextResponse.json({
        error: rateLimitResult.status.reason,
        rateLimitStatus: rateLimitResult.status
      }, { status: 429 });
    }

    console.log('✅ Rate limit check passed. Remaining: User=' + rateLimitStatus.userRemaining + ', Global=' + rateLimitStatus.globalRemaining);

    const { text } = await request.json();

    if (!text || typeof text !== 'string' || text.trim().length === 0) {
      return NextResponse.json({ 
        error: 'Text input is required' 
      }, { status: 400 });
    }

    // Validate text length (reasonable limit for LLM processing)
    if (text.length > 4000) {
      return NextResponse.json({ 
        error: 'Text too long. Maximum 4000 characters.' 
      }, { status: 400 });
    }

    console.log('Processing text with OpenRouter:', {
      model,
      textLength: text.length,
      prompt: HARDCODED_PROMPT
    });

    // Process text with OpenRouter
    const response = await openai.chat.completions.create({
      model: model,
      messages: [
        {
          role: 'system',
          content: HARDCODED_PROMPT
        },
        {
          role: 'user',
          content: text
        }
      ],
      max_tokens: 1000,
      temperature: 0.7
    });

    console.log('Model used:', model);
    console.log('Response received from OpenRouter');

    const processedText = response.choices[0]?.message?.content;

    if (!processedText) {
      console.error('No processed text found in OpenRouter response');

      // Log the failed generation attempt
      await logGenerationAttempt({
        userId,
        prompt: text,
        success: false,
        errorMessage: `No processed text found. Model: ${model}`
      });

      throw new Error(`No processed text found. Model: ${model}`);
    }

    // Generation was successful - decrement the rate limits
    console.log('✅ Generation successful, decrementing rate limits...');
    const decrementSuccess = await decrementLimits(userId);

    if (!decrementSuccess) {
      console.error('⚠️ Failed to decrement rate limits after successful generation');
      // Continue anyway since the generation was successful
    }

    // Log the successful generation attempt
    await logGenerationAttempt({
      userId,
      prompt: text,
      success: true,
      imageUrl: undefined // No image for text processing
    });

    console.log('🎉 Text processing completed successfully');

    return NextResponse.json({
      success: true,
      processedText,
      originalText: text,
      processedAt: new Date().toISOString(),
      model: model,
      rateLimitStatus: {
        userRemaining: Math.max(0, rateLimitStatus.userRemaining - 1),
        globalRemaining: Math.max(0, rateLimitStatus.globalRemaining - 1),
        userLimit: rateLimitStatus.userLimit,
        globalLimit: rateLimitStatus.globalLimit
      }
    });

  } catch (error) {
    console.error('Text processing error:', error);

    // Log the failed generation attempt if we have user info
    if (userId > 0) {
      await logGenerationAttempt({
        userId,
        prompt: 'Error occurred during text processing',
        success: false,
        errorMessage: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    // Handle specific OpenRouter/OpenAI errors
    if (error instanceof OpenAI.APIError) {
      console.error('🔥 OpenRouter API Error:', {
        status: error.status,
        message: error.message,
        userId: userId
      });

      if (error.status === 401) {
        return NextResponse.json({
          error: 'Service temporarily unavailable. Please try again later.'
        }, { status: 500 });
      }
      if (error.status === 429) {
        return NextResponse.json({
          error: 'Service is busy. Please try again in a few minutes.'
        }, { status: 429 });
      }
      if (error.status === 400) {
        return NextResponse.json({
          error: 'Invalid text input. Please check your input and try again.'
        }, { status: 400 });
      }
      return NextResponse.json({
        error: 'Text processing failed. Please try again later.'
      }, { status: 500 });
    }

    return NextResponse.json({
      error: 'Text processing failed. Please try again.'
    }, { status: 500 });
  }
}