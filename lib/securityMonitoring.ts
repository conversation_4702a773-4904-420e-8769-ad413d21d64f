interface SecurityEvent {
  type: 'auth_failure' | 'rate_limit_exceeded' | 'suspicious_activity' | 'error';
  userId?: string;
  ip?: string;
  userAgent?: string;
  details: string;
  timestamp: Date;
}

class SecurityMonitor {
  private events: SecurityEvent[] = [];
  private readonly maxEvents = 1000;

  logSecurityEvent(event: Omit<SecurityEvent, 'timestamp'>) {
    const securityEvent: SecurityEvent = {
      ...event,
      timestamp: new Date()
    };

    this.events.push(securityEvent);

    // Keep only recent events
    if (this.events.length > this.maxEvents) {
      this.events = this.events.slice(-this.maxEvents);
    }

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.warn('🚨 Security Event:', securityEvent);
    }

    // In production, you would send this to your monitoring service
    // Example: Sentry, DataDog, CloudWatch, etc.
    this.sendToMonitoringService(securityEvent);
  }

  private async sendToMonitoringService(event: SecurityEvent) {
    // Implement your monitoring service integration here
    // For now, just log critical events
    if (event.type === 'suspicious_activity') {
      console.error('🚨 CRITICAL SECURITY EVENT:', event);
    }
  }

  getRecentEvents(limit: number = 50): SecurityEvent[] {
    return this.events.slice(-limit);
  }

  getEventsByType(type: SecurityEvent['type']): SecurityEvent[] {
    return this.events.filter(event => event.type === type);
  }

  // Detect suspicious patterns
  detectSuspiciousActivity(ip: string): boolean {
    const recentEvents = this.events.filter(
      event => event.ip === ip && 
      event.timestamp > new Date(Date.now() - 60 * 60 * 1000) // Last hour
    );

    // Multiple failed auth attempts
    const authFailures = recentEvents.filter(e => e.type === 'auth_failure').length;
    if (authFailures > 10) {
      this.logSecurityEvent({
        type: 'suspicious_activity',
        ip,
        details: `Multiple authentication failures: ${authFailures} in the last hour`
      });
      return true;
    }

    // Rate limit exceeded multiple times
    const rateLimitEvents = recentEvents.filter(e => e.type === 'rate_limit_exceeded').length;
    if (rateLimitEvents > 5) {
      this.logSecurityEvent({
        type: 'suspicious_activity',
        ip,
        details: `Multiple rate limit violations: ${rateLimitEvents} in the last hour`
      });
      return true;
    }

    return false;
  }
}

export const securityMonitor = new SecurityMonitor();

// Helper functions for common security events
export function logAuthFailure(ip: string, userAgent: string, details: string) {
  securityMonitor.logSecurityEvent({
    type: 'auth_failure',
    ip,
    userAgent,
    details
  });
}

export function logRateLimitExceeded(userId: string, ip: string, endpoint: string) {
  securityMonitor.logSecurityEvent({
    type: 'rate_limit_exceeded',
    userId,
    ip,
    details: `Rate limit exceeded for endpoint: ${endpoint}`
  });
}

export function logSecurityError(error: string, userId?: string, ip?: string) {
  securityMonitor.logSecurityEvent({
    type: 'error',
    userId,
    ip,
    details: error
  });
}
