import { createClient } from "@/lib/supabase/server";
import { NextResponse } from "next/server";
import { type NextRequest } from "next/server";

export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url);
  const code = requestUrl.searchParams.get("code");
  const error = requestUrl.searchParams.get("error");
  const error_description = requestUrl.searchParams.get("error_description");

  // Handle OAuth errors
  if (error || error_description) {
    console.error("OAuth callback error:", { error, error_description });
    return NextResponse.redirect(
      `${requestUrl.origin}/auth/error?error=${encodeURIComponent(
        error_description || error || "Authentication failed"
      )}`
    );
  }

  if (code) {
    const supabase = await createClient();
    
    try {
      // Exchange the code for a session
      const { data, error: exchangeError } = await supabase.auth.exchangeCodeForSession(code);
      
      if (exchangeError) {
        console.error("Code exchange error:", exchangeError);
        return NextResponse.redirect(
          `${requestUrl.origin}/auth/error?error=${encodeURIComponent(
            exchangeError.message
          )}`
        );
      }

      if (data?.session) {
        // Successful authentication, redirect to editor
        return NextResponse.redirect(`${requestUrl.origin}/editor`);
      }
    } catch (err) {
      console.error("Unexpected error during OAuth callback:", err);
      return NextResponse.redirect(
        `${requestUrl.origin}/auth/error?error=${encodeURIComponent(
          "Authentication failed. Please try again."
        )}`
      );
    }
  }

  // No code parameter found
  return NextResponse.redirect(
    `${requestUrl.origin}/auth/error?error=${encodeURIComponent(
      "Invalid authentication request"
    )}`
  );
}